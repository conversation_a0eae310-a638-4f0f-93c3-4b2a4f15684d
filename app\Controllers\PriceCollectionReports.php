<?php

namespace App\Controllers;

use App\Models\ActivityPriceCollectionDataModel;
use App\Models\GoodsGroupModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsItemModel;
use App\Models\BusinessEntityModel;
use App\Models\BusinessLocationModel;
use App\Models\GeoDistrictModel;
use App\Models\GeoProvinceModel;
use App\Models\UserModel;

class PriceCollectionReports extends BaseController
{
    protected $priceDataModel;
    protected $goodsGroupModel;
    protected $goodsBrandModel;
    protected $goodsItemModel;
    protected $businessEntityModel;
    protected $businessLocationModel;
    protected $geoDistrictModel;
    protected $geoProvinceModel;
    protected $userModel;

    public function __construct()
    {
        $this->priceDataModel = new ActivityPriceCollectionDataModel();
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsItemModel = new GoodsItemModel();
        $this->businessEntityModel = new BusinessEntityModel();
        $this->businessLocationModel = new BusinessLocationModel();
        $this->geoDistrictModel = new GeoDistrictModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->userModel = new UserModel();
    }

    /**
     * Check admin authentication
     */
    private function checkAdminAuth()
    {
        if (!session()->get('logged_in') || 
            (session()->get('is_admin') != 1 && session()->get('is_supervisor') != 1)) {
            return redirect()->to('admin')->with('error', 'Please login to access admin portal.');
        }
        return null;
    }

    /**
     * GET: admin/price-collection-reports/raw
     * Display raw price collection data for the last 12 months
     */
    public function raw()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('org_id');

        // Get price data for the last 12 months
        $rawData = $this->getRawPriceData($orgId);

        $data = [
            'title' => 'Raw Price Collection Report',
            'rawData' => $rawData
        ];

        return view('price_collection_reports/price_collection_reports_raw', $data);
    }

    /**
     * GET: admin/price-collection-reports/summary
     * Display summary reports with charts and analytics
     */
    public function summary()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('org_id');

        // Get all summary data
        $summaryTableData = $this->getSummaryTableData($orgId);
        $inflationData = $this->getInflationData($orgId);
        $districtInflationData = $this->getDistrictInflationData($orgId);
        $inflationTrendData = $this->getInflationTrendData($orgId);
        $priceTrendData = $this->getPriceTrendData($orgId);

        $data = [
            'title' => 'Price Collection Summary Reports',
            'summaryTableData' => $summaryTableData,
            'inflationData' => $inflationData,
            'districtInflationData' => $districtInflationData,
            'inflationTrendData' => $inflationTrendData,
            'priceTrendData' => $priceTrendData
        ];

        return view('price_collection_reports/price_collection_reports_summary', $data);
    }

    /**
     * Get raw price collection data with all related information
     */
    private function getRawPriceData($orgId)
    {
        // Calculate date 12 months ago
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        // Get price data with joins
        $priceData = $this->priceDataModel
            ->select('
                activity_price_collection_data.id,
                activity_price_collection_data.price,
                activity_price_collection_data.created_at,
                activity_price_collection_data.updated_at,
                goods_groups.group_name,
                goods_brands.brand_name,
                goods_brands.type as brand_type,
                goods_items.item as item_name,
                business_locations.business_name as location_name,
                business_locations.type as business_type,
                business_entities.business_name as entity_name,
                geo_districts.name as district_name,
                geo_provinces.name as province_name,
                created_user.name as created_by_name,
                updated_user.name as updated_by_name
            ')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('goods_brands', 'goods_brands.id = goods_items.goods_brand_id', 'left')
            ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
            ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
            ->join('business_entities', 'business_entities.id = business_locations.business_entity_id', 'left')
            ->join('geo_districts', 'geo_districts.id = business_locations.district_id', 'left')
            ->join('geo_provinces', 'geo_provinces.id = business_locations.province_id', 'left')
            ->join('users as created_user', 'created_user.id = activity_price_collection_data.created_by', 'left')
            ->join('users as updated_user', 'updated_user.id = activity_price_collection_data.updated_by', 'left')
            ->where('activity_price_collection_data.org_id', $orgId)
            ->where('activity_price_collection_data.created_at >=', $twelveMonthsAgo)
            ->where('(activity_price_collection_data.is_deleted IS NULL OR activity_price_collection_data.is_deleted = 0)')
            ->orderBy('activity_price_collection_data.id', 'DESC')
            ->findAll();

        return $priceData;
    }

    /**
     * Get summary table data with statistics for each goods group
     */
    private function getSummaryTableData($orgId)
    {
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        $query = "
            SELECT
                gg.group_name,
                COUNT(apcd.id) as total_records,
                ROUND(AVG(apcd.price), 2) as avg_price,
                ROUND(MAX(apcd.price), 2) as high_price,
                ROUND(MIN(apcd.price), 2) as low_price,
                ROUND(STDDEV_SAMP(apcd.price), 2) as price_deviation
            FROM activity_price_collection_data apcd
            JOIN goods_items gi ON gi.id = apcd.item_id
            JOIN goods_brands gb ON gb.id = gi.goods_brand_id
            JOIN goods_groups gg ON gg.id = gi.goods_group_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
            GROUP BY gg.id, gg.group_name
            HAVING COUNT(apcd.id) > 0
            ORDER BY gg.group_name
        ";

        $result = $this->priceDataModel->db->query($query, [$orgId, $twelveMonthsAgo]);
        return $result->getResultArray();
    }

    /**
     * Get overall inflation data for the organization
     */
    private function getInflationData($orgId)
    {
        $currentMonth = date('Y-m-01');
        $twelveMonthsAgo = date('Y-m-01', strtotime('-12 months'));
        $oneMonthAgo = date('Y-m-01', strtotime('-1 month'));

        // Get current month average price
        $currentQuery = "
            SELECT AVG(apcd.price) as avg_price
            FROM activity_price_collection_data apcd
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
        ";

        // Get 12 months ago average price
        $oldQuery = "
            SELECT AVG(apcd.price) as avg_price
            FROM activity_price_collection_data apcd
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.created_at < ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
        ";

        $currentResult = $this->priceDataModel->db->query($currentQuery, [$orgId, $oneMonthAgo]);
        $currentPrice = $currentResult->getRowArray()['avg_price'] ?? 0;

        $oldResult = $this->priceDataModel->db->query($oldQuery, [$orgId, $twelveMonthsAgo, date('Y-m-01', strtotime('-11 months'))]);
        $oldPrice = $oldResult->getRowArray()['avg_price'] ?? 0;

        $inflationRate = 0;
        if ($oldPrice > 0 && $currentPrice > 0) {
            $inflationRate = (($currentPrice - $oldPrice) / $oldPrice) * 100;
        }

        return [
            'current_avg_price' => round($currentPrice, 2),
            'old_avg_price' => round($oldPrice, 2),
            'inflation_rate' => round($inflationRate, 2),
            'total_records' => $this->getTotalRecordsCount($orgId)
        ];
    }

    /**
     * Get district-wise inflation data
     */
    private function getDistrictInflationData($orgId)
    {
        $currentMonth = date('Y-m-01');
        $twelveMonthsAgo = date('Y-m-01', strtotime('-12 months'));
        $oneMonthAgo = date('Y-m-01', strtotime('-1 month'));

        $query = "
            SELECT
                gd.name as district_name,
                AVG(CASE WHEN apcd.created_at >= ? THEN apcd.price END) as current_avg_price,
                AVG(CASE WHEN apcd.created_at >= ? AND apcd.created_at < ? THEN apcd.price END) as old_avg_price
            FROM activity_price_collection_data apcd
            JOIN business_locations bl ON bl.id = apcd.business_location_id
            JOIN geo_districts gd ON gd.id = bl.district_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
                AND gd.name IS NOT NULL
            GROUP BY gd.id, gd.name
            HAVING current_avg_price IS NOT NULL OR old_avg_price IS NOT NULL
            ORDER BY gd.name
        ";

        $result = $this->priceDataModel->db->query($query, [
            $oneMonthAgo,
            $twelveMonthsAgo,
            date('Y-m-01', strtotime('-11 months')),
            $orgId,
            $twelveMonthsAgo
        ]);

        $districts = $result->getResultArray();

        // Calculate inflation rate for each district
        foreach ($districts as &$district) {
            $currentPrice = $district['current_avg_price'] ?? 0;
            $oldPrice = $district['old_avg_price'] ?? 0;

            $inflationRate = 0;
            if ($oldPrice > 0 && $currentPrice > 0) {
                $inflationRate = (($currentPrice - $oldPrice) / $oldPrice) * 100;
            }

            $district['inflation_rate'] = round($inflationRate, 2);
            $district['current_avg_price'] = round($currentPrice, 2);
            $district['old_avg_price'] = round($oldPrice, 2);
        }

        return $districts;
    }

    /**
     * Get inflation trend data for the last 12 months
     */
    private function getInflationTrendData($orgId)
    {
        $months = [];
        $inflationRates = [];

        // Generate last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $monthStart = date('Y-m-01', strtotime("-$i months"));
            $monthEnd = date('Y-m-t', strtotime("-$i months"));
            $prevMonthStart = date('Y-m-01', strtotime("-" . ($i + 1) . " months"));
            $prevMonthEnd = date('Y-m-t', strtotime("-" . ($i + 1) . " months"));

            // Get current month average
            $currentQuery = "
                SELECT AVG(apcd.price) as avg_price
                FROM activity_price_collection_data apcd
                WHERE apcd.org_id = ?
                    AND apcd.created_at >= ?
                    AND apcd.created_at <= ?
                    AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                    AND apcd.status IN ('approved', 'submitted')
            ";

            // Get previous month average
            $prevQuery = "
                SELECT AVG(apcd.price) as avg_price
                FROM activity_price_collection_data apcd
                WHERE apcd.org_id = ?
                    AND apcd.created_at >= ?
                    AND apcd.created_at <= ?
                    AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                    AND apcd.status IN ('approved', 'submitted')
            ";

            $currentResult = $this->priceDataModel->db->query($currentQuery, [$orgId, $monthStart, $monthEnd]);
            $currentPrice = $currentResult->getRowArray()['avg_price'] ?? 0;

            $prevResult = $this->priceDataModel->db->query($prevQuery, [$orgId, $prevMonthStart, $prevMonthEnd]);
            $prevPrice = $prevResult->getRowArray()['avg_price'] ?? 0;

            $inflationRate = 0;
            if ($prevPrice > 0 && $currentPrice > 0) {
                $inflationRate = (($currentPrice - $prevPrice) / $prevPrice) * 100;
            }

            $months[] = date('M Y', strtotime($monthStart));
            $inflationRates[] = round($inflationRate, 2);
        }

        return [
            'months' => $months,
            'inflation_rates' => $inflationRates
        ];
    }

    /**
     * Get price trend data for all goods groups over the last 12 months
     */
    private function getPriceTrendData($orgId)
    {
        $months = [];
        $goodsGroups = [];

        // Generate last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $months[] = date('M Y', strtotime("-$i months"));
        }

        // Get all goods groups with data
        $groupsQuery = "
            SELECT DISTINCT gg.id, gg.group_name
            FROM activity_price_collection_data apcd
            JOIN goods_items gi ON gi.id = apcd.item_id
            JOIN goods_brands gb ON gb.id = gi.goods_brand_id
            JOIN goods_groups gg ON gg.id = gi.goods_group_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
            ORDER BY gg.group_name
        ";

        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));
        $groupsResult = $this->priceDataModel->db->query($groupsQuery, [$orgId, $twelveMonthsAgo]);
        $groups = $groupsResult->getResultArray();

        // Get price data for each group and month
        foreach ($groups as $group) {
            $groupData = [
                'label' => $group['group_name'],
                'data' => []
            ];

            for ($i = 11; $i >= 0; $i--) {
                $monthStart = date('Y-m-01', strtotime("-$i months"));
                $monthEnd = date('Y-m-t', strtotime("-$i months"));

                $priceQuery = "
                    SELECT AVG(apcd.price) as avg_price
                    FROM activity_price_collection_data apcd
                    JOIN goods_items gi ON gi.id = apcd.item_id
                    JOIN goods_brands gb ON gb.id = gi.goods_brand_id
                    WHERE gb.goods_group_id = ?
                        AND apcd.org_id = ?
                        AND apcd.created_at >= ?
                        AND apcd.created_at <= ?
                        AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                        AND apcd.status IN ('approved', 'submitted')
                ";

                $priceResult = $this->priceDataModel->db->query($priceQuery, [$group['id'], $orgId, $monthStart, $monthEnd]);
                $avgPrice = $priceResult->getRowArray()['avg_price'] ?? null;

                $groupData['data'][] = $avgPrice ? round($avgPrice, 2) : null;
            }

            $goodsGroups[] = $groupData;
        }

        return [
            'months' => $months,
            'goods_groups' => $goodsGroups
        ];
    }

    /**
     * Get total records count for the last 12 months
     */
    private function getTotalRecordsCount($orgId)
    {
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        $query = "
            SELECT COUNT(*) as total_count
            FROM activity_price_collection_data apcd
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND (apcd.is_deleted IS NULL OR apcd.is_deleted = 0)
                AND apcd.status IN ('approved', 'submitted')
        ";

        $result = $this->priceDataModel->db->query($query, [$orgId, $twelveMonthsAgo]);
        return $result->getRowArray()['total_count'] ?? 0;
    }
}
