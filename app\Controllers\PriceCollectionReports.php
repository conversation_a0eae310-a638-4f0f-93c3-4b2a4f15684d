<?php

namespace App\Controllers;

use App\Models\ActivityPriceCollectionDataModel;
use App\Models\GoodsGroupModel;
use App\Models\GoodsBrandModel;
use App\Models\GoodsItemModel;
use App\Models\BusinessEntityModel;
use App\Models\BusinessLocationModel;
use App\Models\GeoDistrictModel;
use App\Models\GeoProvinceModel;
use App\Models\UserModel;

class PriceCollectionReports extends BaseController
{
    protected $priceDataModel;
    protected $goodsGroupModel;
    protected $goodsBrandModel;
    protected $goodsItemModel;
    protected $businessEntityModel;
    protected $businessLocationModel;
    protected $geoDistrictModel;
    protected $geoProvinceModel;
    protected $userModel;

    public function __construct()
    {
        $this->priceDataModel = new ActivityPriceCollectionDataModel();
        $this->goodsGroupModel = new GoodsGroupModel();
        $this->goodsBrandModel = new GoodsBrandModel();
        $this->goodsItemModel = new GoodsItemModel();
        $this->businessEntityModel = new BusinessEntityModel();
        $this->businessLocationModel = new BusinessLocationModel();
        $this->geoDistrictModel = new GeoDistrictModel();
        $this->geoProvinceModel = new GeoProvinceModel();
        $this->userModel = new UserModel();
    }

    /**
     * Check admin authentication
     */
    private function checkAdminAuth()
    {
        if (!session()->get('logged_in') || 
            (session()->get('is_admin') != 1 && session()->get('is_supervisor') != 1)) {
            return redirect()->to('admin')->with('error', 'Please login to access admin portal.');
        }
        return null;
    }

    /**
     * GET: admin/price-collection-reports/raw
     * Display raw price collection data for the last 12 months
     */
    public function raw()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('org_id');

        // Get price data for the last 12 months
        $rawData = $this->getRawPriceData($orgId);

        $data = [
            'title' => 'Raw Price Collection Report',
            'rawData' => $rawData
        ];

        return view('price_collection_reports/price_collection_reports_raw', $data);
    }

    /**
     * GET: admin/price-collection-reports/summary
     * Display summary reports with charts and analytics
     */
    public function summary()
    {
        $authCheck = $this->checkAdminAuth();
        if ($authCheck) return $authCheck;

        $orgId = session()->get('org_id');

        // Get all summary data
        $summaryTableData = $this->getSummaryTableData($orgId);
        $inflationData = $this->getInflationData($orgId);
        $districtInflationData = $this->getDistrictInflationData($orgId);
        $inflationTrendData = $this->getInflationTrendData($orgId);
        $priceTrendData = $this->getPriceTrendData($orgId);

        $data = [
            'title' => 'Price Collection Summary Reports',
            'summaryTableData' => $summaryTableData,
            'inflationData' => $inflationData,
            'districtInflationData' => $districtInflationData,
            'inflationTrendData' => $inflationTrendData,
            'priceTrendData' => $priceTrendData
        ];

        return view('price_collection_reports/price_collection_reports_summary', $data);
    }

    /**
     * Get raw price collection data with all related information
     */
    private function getRawPriceData($orgId)
    {
        // Calculate date 12 months ago
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        // Get price data with joins
        $priceData = $this->priceDataModel
            ->select('
                activity_price_collection_data.id,
                activity_price_collection_data.price,
                activity_price_collection_data.created_at,
                activity_price_collection_data.updated_at,
                goods_groups.group_name,
                goods_brands.brand_name,
                goods_brands.type as brand_type,
                goods_items.item as item_name,
                business_locations.business_name as location_name,
                business_locations.type as business_type,
                business_entities.business_name as entity_name,
                geo_districts.name as district_name,
                geo_provinces.name as province_name,
                created_user.name as created_by_name,
                updated_user.name as updated_by_name
            ')
            ->join('goods_items', 'goods_items.id = activity_price_collection_data.item_id', 'left')
            ->join('goods_brands', 'goods_brands.id = goods_items.goods_brand_id', 'left')
            ->join('goods_groups', 'goods_groups.id = goods_brands.goods_group_id', 'left')
            ->join('business_locations', 'business_locations.id = activity_price_collection_data.business_location_id', 'left')
            ->join('business_entities', 'business_entities.id = business_locations.business_entity_id', 'left')
            ->join('geo_districts', 'geo_districts.id = business_locations.district_id', 'left')
            ->join('geo_provinces', 'geo_provinces.id = business_locations.province_id', 'left')
            ->join('users as created_user', 'created_user.id = activity_price_collection_data.created_by', 'left')
            ->join('users as updated_user', 'updated_user.id = activity_price_collection_data.updated_by', 'left')
            ->where('activity_price_collection_data.org_id', $orgId)
            ->where('activity_price_collection_data.created_at >=', $twelveMonthsAgo)
            ->where('(activity_price_collection_data.is_deleted IS NULL OR activity_price_collection_data.is_deleted = 0)')
            ->orderBy('activity_price_collection_data.id', 'DESC')
            ->findAll();

        return $priceData;
    }

    /**
     * Get summary table data with statistics for each goods group
     */
    private function getSummaryTableData($orgId)
    {
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        $query = "
            SELECT
                gg.group_name,
                COUNT(apcd.id) as total_records,
                ROUND(AVG(apcd.price), 2) as avg_price,
                ROUND(MAX(apcd.price), 2) as high_price,
                ROUND(MIN(apcd.price), 2) as low_price,
                ROUND(STDDEV_SAMP(apcd.price), 2) as price_deviation
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            JOIN goods_items gi ON gi.id = apcd.item_id
            JOIN goods_brands gb ON gb.id = gi.goods_brand_id
            JOIN goods_groups gg ON gg.id = gi.goods_group_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
            GROUP BY gg.id, gg.group_name
            HAVING COUNT(apcd.id) > 0
            ORDER BY gg.group_name
        ";

        $result = $this->priceDataModel->db->query($query, [$orgId, $twelveMonthsAgo]);
        return $result->getResultArray();
    }

    /**
     * Get overall CPI data for the organization using same items comparison
     */
    private function getInflationData($orgId)
    {
        $currentMonth = date('Y-m-01');
        $twelveMonthsAgo = date('Y-m-01', strtotime('-12 months'));
        $oneMonthAgo = date('Y-m-01', strtotime('-1 month'));

        // Get CPI using same items comparison approach
        $cpiQuery = "
            SELECT
                SUM(current_prices.avg_price) as current_total,
                SUM(base_prices.avg_price) as base_total,
                COUNT(DISTINCT current_prices.item_id) as common_items
            FROM (
                SELECT
                    apcd.item_id,
                    AVG(apcd.price) as avg_price
                FROM activity_price_collection_data apcd
                JOIN activities a ON a.id = apcd.activity_id
                WHERE apcd.org_id = ?
                    AND apcd.created_at >= ?
                    AND apcd.deleted_at IS NULL
                    AND apcd.status = 'active'
                    AND a.status = 'approved'
                    AND a.is_deleted = 0
                GROUP BY apcd.item_id
            ) current_prices
            INNER JOIN (
                SELECT
                    apcd.item_id,
                    AVG(apcd.price) as avg_price
                FROM activity_price_collection_data apcd
                JOIN activities a ON a.id = apcd.activity_id
                WHERE apcd.org_id = ?
                    AND apcd.created_at >= ?
                    AND apcd.created_at < ?
                    AND apcd.deleted_at IS NULL
                    AND apcd.status = 'active'
                    AND a.status = 'approved'
                    AND a.is_deleted = 0
                GROUP BY apcd.item_id
            ) base_prices ON current_prices.item_id = base_prices.item_id
        ";

        $result = $this->priceDataModel->db->query($cpiQuery, [
            $orgId, $oneMonthAgo,
            $orgId, $twelveMonthsAgo, date('Y-m-01', strtotime('-11 months'))
        ]);

        $cpiData = $result->getRowArray();

        $currentTotal = $cpiData['current_total'] ?? 0;
        $baseTotal = $cpiData['base_total'] ?? 0;
        $commonItems = $cpiData['common_items'] ?? 0;

        // Calculate CPI using basket of same items
        // CPI = (Sum of Current Prices for Same Items / Sum of Base Prices for Same Items) × 100
        $cpi = 0;
        if ($baseTotal > 0 && $currentTotal > 0) {
            $cpi = ($currentTotal / $baseTotal) * 100;
        }

        // Get current period average for display
        $currentAvgQuery = "
            SELECT AVG(apcd.price) as avg_price
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
        ";

        $currentAvgResult = $this->priceDataModel->db->query($currentAvgQuery, [$orgId, $oneMonthAgo]);
        $currentAvgPrice = $currentAvgResult->getRowArray()['avg_price'] ?? 0;

        return [
            'current_avg_price' => round($currentAvgPrice, 2),
            'base_avg_price' => round($baseTotal / max($commonItems, 1), 2),
            'cpi' => round($cpi, 2),
            'common_items' => $commonItems,
            'total_records' => $this->getTotalRecordsCount($orgId)
        ];
    }

    /**
     * Get district-wise CPI data using same items comparison
     */
    private function getDistrictInflationData($orgId)
    {
        $currentMonth = date('Y-m-01');
        $twelveMonthsAgo = date('Y-m-01', strtotime('-12 months'));
        $oneMonthAgo = date('Y-m-01', strtotime('-1 month'));

        // Get all districts first
        $districtsQuery = "
            SELECT DISTINCT gd.id, gd.name as district_name
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            JOIN business_locations bl ON bl.id = apcd.business_location_id
            JOIN geo_districts gd ON gd.id = bl.district_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
                AND gd.name IS NOT NULL
            ORDER BY gd.name
        ";

        $districtsResult = $this->priceDataModel->db->query($districtsQuery, [$orgId, $twelveMonthsAgo]);
        $districts = $districtsResult->getResultArray();

        // Calculate CPI for each district using same items approach
        foreach ($districts as &$district) {
            $districtId = $district['id'];

            $cpiQuery = "
                SELECT
                    SUM(current_prices.avg_price) as current_total,
                    SUM(base_prices.avg_price) as base_total,
                    COUNT(DISTINCT current_prices.item_id) as common_items
                FROM (
                    SELECT
                        apcd.item_id,
                        AVG(apcd.price) as avg_price
                    FROM activity_price_collection_data apcd
                    JOIN activities a ON a.id = apcd.activity_id
                    JOIN business_locations bl ON bl.id = apcd.business_location_id
                    WHERE apcd.org_id = ?
                        AND bl.district_id = ?
                        AND apcd.created_at >= ?
                        AND apcd.deleted_at IS NULL
                        AND apcd.status = 'active'
                        AND a.status = 'approved'
                        AND a.is_deleted = 0
                    GROUP BY apcd.item_id
                ) current_prices
                INNER JOIN (
                    SELECT
                        apcd.item_id,
                        AVG(apcd.price) as avg_price
                    FROM activity_price_collection_data apcd
                    JOIN activities a ON a.id = apcd.activity_id
                    JOIN business_locations bl ON bl.id = apcd.business_location_id
                    WHERE apcd.org_id = ?
                        AND bl.district_id = ?
                        AND apcd.created_at >= ?
                        AND apcd.created_at < ?
                        AND apcd.deleted_at IS NULL
                        AND apcd.status = 'active'
                        AND a.status = 'approved'
                        AND a.is_deleted = 0
                    GROUP BY apcd.item_id
                ) base_prices ON current_prices.item_id = base_prices.item_id
            ";

            $cpiResult = $this->priceDataModel->db->query($cpiQuery, [
                $orgId, $districtId, $oneMonthAgo,
                $orgId, $districtId, $twelveMonthsAgo, date('Y-m-01', strtotime('-11 months'))
            ]);

            $cpiData = $cpiResult->getRowArray();

            $currentTotal = $cpiData['current_total'] ?? 0;
            $baseTotal = $cpiData['base_total'] ?? 0;
            $commonItems = $cpiData['common_items'] ?? 0;

            // Calculate CPI using basket of same items
            $cpi = 0;
            if ($baseTotal > 0 && $currentTotal > 0) {
                $cpi = ($currentTotal / $baseTotal) * 100;
            }

            $district['cpi'] = round($cpi, 2);
            $district['current_avg_price'] = round($currentTotal / max($commonItems, 1), 2);
            $district['base_avg_price'] = round($baseTotal / max($commonItems, 1), 2);
            $district['common_items'] = $commonItems;
        }

        return $districts;
    }

    /**
     * Get CPI trend data for the last 12 months using same items comparison
     */
    private function getInflationTrendData($orgId)
    {
        $months = [];
        $cpiValues = [];

        // Get base period (12 months ago) for CPI calculation
        $baseMonthStart = date('Y-m-01', strtotime('-12 months'));
        $baseMonthEnd = date('Y-m-t', strtotime('-12 months'));

        // Get base period items and their prices
        $baseItemsQuery = "
            SELECT
                apcd.item_id,
                AVG(apcd.price) as avg_price
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.created_at <= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
            GROUP BY apcd.item_id
        ";

        $baseItemsResult = $this->priceDataModel->db->query($baseItemsQuery, [$orgId, $baseMonthStart, $baseMonthEnd]);
        $baseItems = $baseItemsResult->getResultArray();

        if (empty($baseItems)) {
            // No base period data, return empty arrays
            for ($i = 11; $i >= 0; $i--) {
                $months[] = date('M Y', strtotime("-$i months"));
                $cpiValues[] = 0;
            }
            return [
                'months' => $months,
                'cpi_values' => $cpiValues
            ];
        }

        // Calculate base period total
        $baseTotalPrice = array_sum(array_column($baseItems, 'avg_price'));
        $baseItemIds = array_column($baseItems, 'item_id');

        // Generate last 12 months CPI values
        for ($i = 11; $i >= 0; $i--) {
            $monthStart = date('Y-m-01', strtotime("-$i months"));
            $monthEnd = date('Y-m-t', strtotime("-$i months"));

            // Get current month prices for the same items as base period
            $currentQuery = "
                SELECT
                    apcd.item_id,
                    AVG(apcd.price) as avg_price
                FROM activity_price_collection_data apcd
                JOIN activities a ON a.id = apcd.activity_id
                WHERE apcd.org_id = ?
                    AND apcd.created_at >= ?
                    AND apcd.created_at <= ?
                    AND apcd.deleted_at IS NULL
                    AND apcd.status = 'active'
                    AND a.status = 'approved'
                    AND a.is_deleted = 0
                    AND apcd.item_id IN (" . implode(',', array_fill(0, count($baseItemIds), '?')) . ")
                GROUP BY apcd.item_id
            ";

            $params = array_merge([$orgId, $monthStart, $monthEnd], $baseItemIds);
            $currentResult = $this->priceDataModel->db->query($currentQuery, $params);
            $currentItems = $currentResult->getResultArray();

            // Calculate current period total for same items
            $currentTotalPrice = 0;
            $currentItemsMap = array_column($currentItems, 'avg_price', 'item_id');

            foreach ($baseItems as $baseItem) {
                if (isset($currentItemsMap[$baseItem['item_id']])) {
                    $currentTotalPrice += $currentItemsMap[$baseItem['item_id']];
                }
            }

            // Calculate CPI for this month using same items basket
            $cpi = 0;
            if ($baseTotalPrice > 0 && $currentTotalPrice > 0) {
                $cpi = ($currentTotalPrice / $baseTotalPrice) * 100;
            }

            $months[] = date('M Y', strtotime($monthStart));
            $cpiValues[] = round($cpi, 2);
        }

        return [
            'months' => $months,
            'cpi_values' => $cpiValues
        ];
    }

    /**
     * Get price trend data for all goods groups over the last 12 months
     */
    private function getPriceTrendData($orgId)
    {
        $months = [];
        $goodsGroups = [];

        // Generate last 12 months
        for ($i = 11; $i >= 0; $i--) {
            $months[] = date('M Y', strtotime("-$i months"));
        }

        // Get all goods groups with data
        $groupsQuery = "
            SELECT DISTINCT gg.id, gg.group_name
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            JOIN goods_items gi ON gi.id = apcd.item_id
            JOIN goods_brands gb ON gb.id = gi.goods_brand_id
            JOIN goods_groups gg ON gg.id = gi.goods_group_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
            ORDER BY gg.group_name
        ";

        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));
        $groupsResult = $this->priceDataModel->db->query($groupsQuery, [$orgId, $twelveMonthsAgo]);
        $groups = $groupsResult->getResultArray();

        // Get price data for each group and month
        foreach ($groups as $group) {
            $groupData = [
                'label' => $group['group_name'],
                'data' => []
            ];

            for ($i = 11; $i >= 0; $i--) {
                $monthStart = date('Y-m-01', strtotime("-$i months"));
                $monthEnd = date('Y-m-t', strtotime("-$i months"));

                $priceQuery = "
                    SELECT AVG(apcd.price) as avg_price
                    FROM activity_price_collection_data apcd
                    JOIN activities a ON a.id = apcd.activity_id
                    JOIN goods_items gi ON gi.id = apcd.item_id
                    JOIN goods_brands gb ON gb.id = gi.goods_brand_id
                    WHERE gb.goods_group_id = ?
                        AND apcd.org_id = ?
                        AND apcd.created_at >= ?
                        AND apcd.created_at <= ?
                        AND apcd.deleted_at IS NULL
                        AND apcd.status = 'active'
                        AND a.status = 'approved'
                        AND a.is_deleted = 0
                ";

                $priceResult = $this->priceDataModel->db->query($priceQuery, [$group['id'], $orgId, $monthStart, $monthEnd]);
                $avgPrice = $priceResult->getRowArray()['avg_price'] ?? null;

                $groupData['data'][] = $avgPrice ? round($avgPrice, 2) : null;
            }

            $goodsGroups[] = $groupData;
        }

        return [
            'months' => $months,
            'goods_groups' => $goodsGroups
        ];
    }

    /**
     * Get total records count for the last 12 months
     */
    private function getTotalRecordsCount($orgId)
    {
        $twelveMonthsAgo = date('Y-m-d', strtotime('-12 months'));

        $query = "
            SELECT COUNT(*) as total_count
            FROM activity_price_collection_data apcd
            JOIN activities a ON a.id = apcd.activity_id
            WHERE apcd.org_id = ?
                AND apcd.created_at >= ?
                AND apcd.deleted_at IS NULL
                AND apcd.status = 'active'
                AND a.status = 'approved'
                AND a.is_deleted = 0
        ";

        $result = $this->priceDataModel->db->query($query, [$orgId, $twelveMonthsAgo]);
        return $result->getRowArray()['total_count'] ?? 0;
    }
}
